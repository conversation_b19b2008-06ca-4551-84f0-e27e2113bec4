
// 抖音req_sign逆向Hook代码 + Bd-Ticket-Guard-Client-Data生成
// 在浏览器控制台中运行此代码来拦截关键函数

console.log("🚀 开始Hook抖音签名算法...");

// ==================== Bd-Ticket-Guard-Client-Data 生成模块 ====================

// 模拟 RSA.KEYUTIL (如果环境中没有 jsrsasign 库)
const RSA = {
    KEYUTIL: {
        generateKeypair: function(algorithm, keySize) {
            // 这里应该使用真实的 RSA 密钥生成
            // 为了演示，我们使用模拟数据
            console.log(`生成 ${algorithm} 密钥对，密钥长度: ${keySize}`);

            return {
                prvKeyObj: {
                    // 模拟私钥对象
                    n: "mock_private_key_n",
                    e: "mock_private_key_e",
                    d: "mock_private_key_d"
                },
                pubKeyObj: {
                    // 模拟公钥对象
                    n: "mock_public_key_n",
                    e: "mock_public_key_e"
                }
            };
        },
        getPEM: function(keyObj, format) {
            // 模拟获取 PEM 格式
            if (format === "PKCS8PRV") {
                return "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----";
            } else {
                return "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----";
            }
        },
        getKey: function(pemString) {
            // 模拟从 PEM 字符串获取密钥对象
            return {
                generatePublicKeyHex: function() {
                    return "mock_public_key_hex_" + Date.now();
                }
            };
        }
    }
};

// 生成密钥对
const i = RSA.KEYUTIL.generateKeypair("EC", "secp256r1");
const o = i.prvKeyObj;
const s = i.pubKeyObj;
const publicKeyPEM = RSA.KEYUTIL.getPEM(s);
const privateKeyPEM = RSA.KEYUTIL.getPEM(o, "PKCS8PRV");

console.log("🔑 公钥PEM:", publicKeyPEM);
console.log("🔐 私钥PEM:", privateKeyPEM);

// 模拟加密函数 m
var m = function(t) {
    // 这里应该是实际的加密逻辑
    // 为了演示，返回一个模拟的加密结果
    return "encrypted_" + btoa(JSON.stringify(t)) + "_" + Date.now();
};

// 获取 bd_ticket_guard_ree_public_key
function get_bd_ticket_guard_ree_public_key() {
    var e = m(function(t) {
        // 实际的加密逻辑应该在这里
        return t;
    })(RSA.KEYUTIL.getKey(privateKeyPEM).generatePublicKeyHex());
    return e;
}

// 获取 cookie 中的 bd_ticket_guard_client_data
function get_ck_bd_ticket_guard() {
    try {
        // 从 cookie 中获取 bd_ticket_guard_client_data
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'bd_ticket_guard_client_data') {
                return decodeURIComponent(value);
            }
        }

        // 如果 cookie 中没有，生成一个新的
        const clientData = {
            timestamp: Date.now(),
            public_key: get_bd_ticket_guard_ree_public_key(),
            version: "1.0",
            platform: "web"
        };

        const encodedData = btoa(JSON.stringify(clientData));

        // 设置到 cookie 中
        document.cookie = `bd_ticket_guard_client_data=${encodeURIComponent(encodedData)}; path=/; domain=.douyin.com`;

        return encodedData;
    } catch (error) {
        console.error("获取 bd_ticket_guard_client_data 失败:", error);
        return null;
    }
}

// 输出结果
console.log("🎫 bd_ticket_guard_client_data:", JSON.stringify(get_ck_bd_ticket_guard(), null, 2));

// Hook HMAC相关函数
if (window.crypto && window.crypto.subtle) {
    const originalSign = window.crypto.subtle.sign;
    window.crypto.subtle.sign = function(...args) {
        console.log("🔐 crypto.subtle.sign 被调用:", args);
        return originalSign.apply(this, args);
    };
}

// Hook可能的签名函数
const hookFunction = (obj, funcName) => {
    if (obj && obj[funcName]) {
        const original = obj[funcName];
        obj[funcName] = function(...args) {
            console.log(`🎯 ${funcName} 被调用:`, args);
            const result = original.apply(this, args);
            console.log(`📤 ${funcName} 返回:`, result);
            return result;
        };
    }
};

// 尝试Hook常见的函数名
['signWithHmac', 'sign', 'hmac', 'digest'].forEach(funcName => {
    // 在全局对象中搜索
    for (let key in window) {
        try {
            if (window[key] && typeof window[key] === 'object') {
                hookFunction(window[key], funcName);
            }
        } catch (e) {}
    }
});

// Hook Base64编码
const originalBtoa = window.btoa;
window.btoa = function(str) {
    if (str.includes('req_sign') || str.includes('bd-ticket-guard')) {
        console.log("📋 Base64编码被调用:", str);
    }
    return originalBtoa.call(this, str);
};

// 监听网络请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const [url, options] = args;
    if (url.includes('douyin.com') || url.includes('aweme')) {
        console.log("🌐 网络请求:", url, options);
    }
    return originalFetch.apply(this, args);
};

console.log("✅ Hook代码已安装，请执行相关操作触发签名算法");
