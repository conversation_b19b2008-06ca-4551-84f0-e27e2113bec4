// Bd-Ticket-Guard-Client-Data 生成器
// 基于图片中的代码转换而来

console.log("🎫 Bd-Ticket-Guard-Client-Data 生成器启动...");

// ==================== RSA 密钥生成模块 ====================

// 模拟 RSA.KEYUTIL (兼容 jsrsasign 库的接口)
const RSA = {
    KEYUTIL: {
        generateKeypair: function(algorithm, curve) {
            console.log(`🔑 生成 ${algorithm} 密钥对，曲线: ${curve}`);
            
            // 模拟椭圆曲线密钥生成
            const timestamp = Date.now();
            const randomSeed = Math.random().toString(36).substring(2);
            
            return {
                prvKeyObj: {
                    // 模拟私钥对象
                    curve: curve,
                    d: "mock_private_key_d_" + timestamp,
                    x: "mock_private_key_x_" + randomSeed,
                    y: "mock_private_key_y_" + randomSeed
                },
                pubKeyObj: {
                    // 模拟公钥对象  
                    curve: curve,
                    x: "mock_public_key_x_" + timestamp,
                    y: "mock_public_key_y_" + randomSeed
                }
            };
        },
        
        getPEM: function(keyObj, format) {
            const timestamp = Date.now();
            if (format === "PKCS8PRV") {
                // 私钥 PEM 格式
                return `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC${timestamp}
mock_private_key_content_${Math.random().toString(36)}
-----END PRIVATE KEY-----`;
            } else {
                // 公钥 PEM 格式
                return `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA${timestamp}
mock_public_key_content_${Math.random().toString(36)}
-----END PUBLIC KEY-----`;
            }
        },
        
        getKey: function(pemString) {
            // 从 PEM 字符串获取密钥对象
            return {
                generatePublicKeyHex: function() {
                    // 生成公钥的十六进制表示
                    const hex = "04" + // 未压缩点标识符
                              Math.random().toString(16).substring(2, 66) + // x 坐标
                              Math.random().toString(16).substring(2, 66);   // y 坐标
                    return hex;
                }
            };
        }
    }
};

// ==================== 核心实现 ====================

// 生成密钥对 (对应图片中的代码)
const i = RSA.KEYUTIL.generateKeypair("EC", "secp256r1");
const o = i.prvKeyObj;
const s = i.pubKeyObj;
const publicKeyPEM = RSA.KEYUTIL.getPEM(s);
const privateKeyPEM = RSA.KEYUTIL.getPEM(o, "PKCS8PRV");

console.log("📋 公钥PEM:", publicKeyPEM);
console.log("📋 私钥PEM:", privateKeyPEM);

// 加密函数 m (对应图片中的 var m = function(t) {...})
var m = function(t) {
    // 模拟加密过程
    if (typeof t === 'function') {
        // 如果传入的是函数，先执行函数
        const result = t();
        return btoa(JSON.stringify({
            encrypted: true,
            data: result,
            timestamp: Date.now(),
            algorithm: "mock_encryption"
        }));
    } else {
        // 直接加密数据
        return btoa(JSON.stringify({
            encrypted: true,
            data: t,
            timestamp: Date.now(),
            algorithm: "mock_encryption"
        }));
    }
};

// 获取 bd_ticket_guard_ree_public_key (对应图片中的函数)
function get_bd_ticket_guard_ree_public_key() {
    var e = m(function(t) {
        // 这里应该是具体的加密逻辑
        console.log("🔐 正在处理公钥数据:", t);
        return t;
    })(RSA.KEYUTIL.getKey(privateKeyPEM).generatePublicKeyHex());
    
    console.log("🎯 生成的 bd_ticket_guard_ree_public_key:", e);
    return e;
}

// 获取 cookie 中的 bd_ticket_guard_client_data (对应图片中的函数)
function get_ck_bd_ticket_guard() {
    try {
        // 生成客户端数据
        const clientData = {
            version: "1.0.0",
            timestamp: Date.now(),
            public_key: get_bd_ticket_guard_ree_public_key(),
            platform: "web",
            browser: navigator.userAgent,
            screen: {
                width: screen.width,
                height: screen.height
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
        
        const jsonString = JSON.stringify(clientData);
        const base64Data = btoa(jsonString);
        
        console.log("📦 生成的客户端数据:", clientData);
        return base64Data;
        
    } catch (error) {
        console.error("❌ 生成 bd_ticket_guard_client_data 失败:", error);
        return null;
    }
}

// ==================== 主要执行逻辑 ====================

// 执行生成过程
console.log("🚀 开始生成 bd_ticket_guard_client_data...");

const bdTicketGuardData = get_ck_bd_ticket_guard();

// 输出最终结果 (对应图片中的 console.log)
console.log("bd_ticket_guard_client_data", JSON.stringify(bdTicketGuardData, null, 2));

// 解码并显示内容以便调试
if (bdTicketGuardData) {
    try {
        const decodedData = JSON.parse(atob(bdTicketGuardData));
        console.log("📋 解码后的数据内容:", decodedData);
    } catch (e) {
        console.error("❌ 解码失败:", e);
    }
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        get_bd_ticket_guard_ree_public_key,
        get_ck_bd_ticket_guard,
        RSA
    };
}

console.log("✅ Bd-Ticket-Guard-Client-Data 生成器运行完成");
